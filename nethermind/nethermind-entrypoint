#!/bin/bash
set -eu

# Default configurations
NETHERMIND_DATA_DIR=${NETHERMIND_DATA_DIR:-/data}
NETHERMIND_LOG_LEVEL=${NETHERMIND_LOG_LEVEL:-Info}
NETWORK=${NETWORK:-mainnet}

RPC_PORT="${RPC_PORT:-8545}"
WS_PORT="${WS_PORT:-8546}"
AUTHRPC_PORT="${AUTHRPC_PORT:-8551}"
METRICS_PORT="${METRICS_PORT:-6060}"
DISCOVERY_PORT="${DISCOVERY_PORT:-30303}"
P2P_PORT="${P2P_PORT:-30303}"
JWT_SECRET_FILE=${JWT_SECRET_FILE:-/tmp/jwt/jwtsecret}
ADDITIONAL_ARGS=""

# Check if required variables are set
if [[ -z "$NETWORK" ]]; then
    echo "Expected NETWORK to be set" 1>&2
    exit 1
fi

# Create necessary directories
mkdir -p "$NETHERMIND_DATA_DIR"

# Write the JWT secret
if [[ -z "$OP_NODE_L2_ENGINE_AUTH_RAW" ]]; then
    echo "Expected OP_NODE_L2_ENGINE_AUTH_RAW to be set" 1>&2
    exit 1
fi
echo "$OP_NODE_L2_ENGINE_AUTH_RAW" > "$OP_NODE_L2_ENGINE_AUTH"

# Additional arguments based on environment variables
if [ "${OP_NETHERMIND_BOOTNODES+x}" = x ]; then
    ADDITIONAL_ARGS="$ADDITIONAL_ARGS --Network.Bootnodes=$OP_NETHERMIND_BOOTNODES"
fi

if [[ -n "${OP_NETHERMIND_ETHSTATS_ENABLED:-}" ]]; then
    ADDITIONAL_ARGS="$ADDITIONAL_ARGS --EthStats.Enabled=$OP_NETHERMIND_ETHSTATS_ENABLED"
fi

if [[ -n "${OP_NETHERMIND_ETHSTATS_ENDPOINT:-}" ]]; then
    ADDITIONAL_ARGS="$ADDITIONAL_ARGS --EthStats.NodeName=${OP_NETHERMIND_ETHSTATS_NODE_NAME:-NethermindNode} --EthStats.Endpoint=$OP_NETHERMIND_ETHSTATS_ENDPOINT"
fi

# Execute Nethermind
exec ./nethermind \
    --config="$OP_NODE_NETWORK" \
    --datadir="$NETHERMIND_DATA_DIR" \
    --Optimism.SequencerUrl=$OP_SEQUENCER_HTTP \
    --log="$NETHERMIND_LOG_LEVEL" \
    --JsonRpc.Enabled=true \
    --JsonRpc.Host=0.0.0.0 \
    --JsonRpc.WebSocketsPort="$WS_PORT" \
    --JsonRpc.Port="$RPC_PORT" \
    --JsonRpc.JwtSecretFile="$OP_NODE_L2_ENGINE_AUTH" \
    --JsonRpc.EngineHost=0.0.0.0 \
    --JsonRpc.EnginePort="$AUTHRPC_PORT" \
    --HealthChecks.Enabled=true \
    --Metrics.Enabled=true \
    --Metrics.ExposePort="$METRICS_PORT" \
    --Network.P2PPort="$P2P_PORT" \
    --Network.DiscoveryPort="$DISCOVERY_PORT" \
    $ADDITIONAL_ARGS
