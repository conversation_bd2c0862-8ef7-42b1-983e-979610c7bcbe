#!/bin/bash
set -eu

VERBOSITY=${GETH_VERBOSITY:-3}
GETH_DATA_DIR=${GETH_DATA_DIR:-/data}
RPC_PORT="${RPC_PORT:-8545}"
WS_PORT="${WS_PORT:-8546}"
AUTHRPC_PORT="${AUTHRPC_PORT:-8551}"
METRICS_PORT="${METRICS_PORT:-6060}"
HOST_IP="" # put your external IP address here and open port 30303 to improve peer connectivity
P2P_PORT="${P2P_PORT:-30303}"
DISCOVERY_PORT="${DISCOVERY_PORT:-30303}"
ADDITIONAL_ARGS=""
OP_GETH_GCMODE="${OP_GETH_GCMODE:-full}"
OP_GETH_SYNCMODE="${OP_GETH_SYNCMODE:-full}"

# Add cache and txpool optimizations with defaults
GETH_CACHE="${GETH_CACHE:-20480}"
GETH_CACHE_DATABASE="${GETH_CACHE_DATABASE:-20}"
GETH_CACHE_GC="${GETH_CACHE_GC:-12}"
GETH_CACHE_SNAPSHOT="${GETH_CACHE_SNAPSHOT:-24}"
GETH_CACHE_TRIE="${GETH_CACHE_TRIE:-44}"
GETH_TXPOOL_GLOBALQUEUE="${GETH_TXPOOL_GLOBALQUEUE:-6144}"
GETH_TXPOOL_GLOBALSLOTS="${GETH_TXPOOL_GLOBALSLOTS:-30072}"
GETH_TXPOOL_LIFETIME="${GETH_TXPOOL_LIFETIME:-10m}"
TXPOOL_PRICE_LIMIT="${TXPOOL_PRICE_LIMIT:-50}"

if [[ -z "$OP_NODE_NETWORK" ]]; then
    echo "expected OP_NODE_NETWORK to be set" 1>&2
    exit 1
fi

mkdir -p $GETH_DATA_DIR

echo "$OP_NODE_L2_ENGINE_AUTH_RAW" > "$OP_NODE_L2_ENGINE_AUTH"

if [ "${OP_GETH_ETH_STATS+x}" = x ]; then
    ADDITIONAL_ARGS="$ADDITIONAL_ARGS --ethstats=$OP_GETH_ETH_STATS"
fi

if [ "${OP_GETH_ALLOW_UNPROTECTED_TXS+x}" = x ]; then
    ADDITIONAL_ARGS="$ADDITIONAL_ARGS --rpc.allow-unprotected-txs=$OP_GETH_ALLOW_UNPROTECTED_TXS"
fi

if [ "${OP_GETH_STATE_SCHEME+x}" = x ]; then
    ADDITIONAL_ARGS="$ADDITIONAL_ARGS --state.scheme=$OP_GETH_STATE_SCHEME"
fi

if [ "${OP_GETH_BOOTNODES+x}" = x ]; then
    ADDITIONAL_ARGS="$ADDITIONAL_ARGS --bootnodes=$OP_GETH_BOOTNODES"
fi

if [ "${HOST_IP:+x}" = x ]; then
    ADDITIONAL_ARGS="$ADDITIONAL_ARGS --nat=extip:$HOST_IP"
fi 

exec ./geth \
    --datadir="$GETH_DATA_DIR" \
    --verbosity="$VERBOSITY" \
    --http \
    --http.corsdomain="*" \
    --http.vhosts="*" \
    --http.addr=0.0.0.0 \
    --http.port="$RPC_PORT" \
    --http.api=web3,debug,eth,net,engine \
    --authrpc.addr=0.0.0.0 \
    --authrpc.port="$AUTHRPC_PORT" \
    --authrpc.vhosts="*" \
    --authrpc.jwtsecret="$OP_NODE_L2_ENGINE_AUTH" \
    --ws \
    --ws.addr=0.0.0.0 \
    --ws.port="$WS_PORT" \
    --ws.origins="*" \
    --ws.api=debug,eth,net,engine \
    --metrics \
    --metrics.addr=0.0.0.0 \
    --metrics.port="$METRICS_PORT" \
    --syncmode="$OP_GETH_SYNCMODE" \
    --gcmode="$OP_GETH_GCMODE" \
    --maxpeers=100 \
    --rollup.sequencerhttp="$OP_GETH_SEQUENCER_HTTP" \
    --rollup.halt=major \
    --op-network="$OP_NODE_NETWORK" \
    --discovery.port="$DISCOVERY_PORT" \
    --port="$P2P_PORT" \
    --rollup.disabletxpoolgossip=true \
    --cache="$GETH_CACHE" \
    --cache.database="$GETH_CACHE_DATABASE" \
    --cache.gc="$GETH_CACHE_GC" \
    --cache.snapshot="$GETH_CACHE_SNAPSHOT" \
    --cache.trie="$GETH_CACHE_TRIE" \
    $ADDITIONAL_ARGS # intentionally unquoted