# Security

## Bug bounty program

In line with our strategy of being the safest way for users to access crypto:

+ Coinbase will be extending our [best-in-industry][1] million-dollar [HackerOne bug bounty program][2]
to cover the Base network, the Base bridge contracts, and Base infrastructure.

+ Coinbase will be working in tandem with OP Labs to harden the security
guarantees of Bedrock and accelerate the timeline for decentralized
fault-proofs on the [OP Stack][3].

+ Coinbase's bug bounty program will run alongside Optimism's existing [Immunefi Bedrock bounty program][4]
to support the open source [Bedrock][5] OP Stack framework.

## Reporting vulnerabilities

All potential vulnerability reports can be submitted via the [HackerOne][6]
platform.

The HackerOne platform allows us to have a centralized and single reporting
source for us to deliver optimized SLA's and results. All reports submitted to
the platform are triaged around the clock by our team of Coinbase engineers
with domain knowledge, ensuring the best quality of review.

For more information on reporting vulnerabilities and our HackerOne bug bounty
program, view our [security program policies][7].

[1]: https://www.coinbase.com/blog/celebrating-10-years-of-our-bug-bounty-program
[2]: https://hackerone.com/coinbase?type=team 
[3]: https://stack.optimism.io/
[4]: https://immunefi.com/bounty/optimism/
[5]: https://stack.optimism.io/docs/releases/bedrock/
[6]: https://hackerone.com/coinbase
[7]: https://hackerone.com/coinbase?view_policy=true


