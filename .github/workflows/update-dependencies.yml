name: Update Dockerfile Dependencies
on:
  schedule:
    - cron: '0 13 * * *'
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  update:
    name: update
    runs-on: ubuntu-latest
    steps:
      - name: Harden the runner (Audit all outbound calls)
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: build dependency updater
        run: cd dependency_updater && go build

      - name: run dependency updater
        id: run_dependency_updater
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: cd dependency_updater && ./dependency_updater --repo ../ --github-action true

      - name: create pull request
        if: ${{ steps.run_dependency_updater.outputs.TITLE != '' }}
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          title: ${{ steps.run_dependency_updater.outputs.TITLE }}
          commit-message: ${{ steps.run_dependency_updater.outputs.TITLE }}
          body: "${{ steps.run_dependency_updater.outputs.DESC }}"
          branch: run-dependency-updater
          delete-branch: true